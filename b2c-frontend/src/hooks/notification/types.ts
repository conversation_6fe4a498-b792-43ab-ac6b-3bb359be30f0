import type {
  AndroidImportance,
  AndroidVisibility,
  AndroidCategory,
  AndroidBigTextStyle,
  AndroidBigPictureStyle,
  AndroidInboxStyle,
  RepeatFrequency,
  AuthorizationStatus,
  IOSNotificationAttachment,
} from '@notifee/react-native';

export interface NotificationData {
  title?: string;
  body?: string;
  imageUrl?: string;
  data?: Record<string, unknown>;
  action?: string;
  category?: string;
  sound?: string;
  badge?: number;
  priority?: 'high' | 'normal' | 'low';
  channelId?: string;
  postId?: string;
  commentId?: string;
  parentCommentId?: string;
  actorProfileId?: string;
}

export interface NotificationPressAction {
  id: string;
  launchActivity?: string;
}

export interface NotificationAction {
  id: string;
  title: string;
  pressAction: NotificationPressAction;
}

export interface AndroidNotificationConfig {
  channelId?: string;
  importance?: AndroidImportance;
  visibility?: AndroidVisibility;
  category?: AndroidCategory;
  groupId?: string;
  groupSummary?: boolean;
  largeIcon?: string;
  smallIcon?: string;
  color?: string;
  lights?: [string, number, number];
  vibrationPattern?: number[];
  autoCancel?: boolean;
  ongoing?: boolean;
  showTimestamp?: boolean;
  style?: AndroidBigTextStyle | AndroidBigPictureStyle | AndroidInboxStyle;
  pressAction?: NotificationPressAction;
  actions?: NotificationAction[];
}

export interface IOSNotificationConfig {
  categoryId?: string;
  threadId?: string;
  targetContentId?: string;
  interruptionLevel?: 'passive' | 'active' | 'timeSensitive' | 'critical';
  sound?: string;
  badge?: number;
  attachments?: IOSNotificationAttachment[];
}

export interface LocalNotificationOptions {
  title: string;
  body: string;
  data?: {
    [key: string]: string | number | object;
  };
  scheduleDate?: Date;
  repeatInterval?: RepeatFrequency;
  sound?: string;
  imageUrl?: string;
  actions?: NotificationAction[];
  android?: AndroidNotificationConfig;
  ios?: IOSNotificationConfig;
  badge?: number;
}

export interface NotificationChannel {
  id: string;
  name: string;
  description?: string;
  importance?: AndroidImportance;
  sound?: string;
  vibration?: boolean;
  vibrationPattern?: number[];
  lights?: boolean;
  lightColor?: string;
  badge?: boolean;
  bypassDnd?: boolean;
}

export interface NotificationSettings {
  authorizationStatus: AuthorizationStatus;
  notificationCenter?: boolean;
  lockScreen?: boolean;
  alert?: boolean;
  badge?: boolean;
  sound?: boolean;
  carPlay?: boolean;
  criticalAlert?: boolean;
  announcement?: boolean;
}

export interface DeviceTokenResponse {
  token: string | null;
  error?: Error;
}

export interface NotificationHookReturn {
  setupNotification: (channels?: NotificationChannel[]) => Promise<boolean>;
  getDeviceToken: (forceRefresh?: boolean) => Promise<string | null>;
  checkNotificationPermissions: () => Promise<boolean>;
  displayNotification: (options: LocalNotificationOptions) => Promise<string>;
  scheduleNotification: (options: LocalNotificationOptions) => Promise<string>;
  cancelNotification: (notificationId: string) => Promise<void>;
  cancelAllNotifications: () => Promise<void>;
  createNotificationChannels: (channels?: NotificationChannel[]) => Promise<void>;
  getNotificationSettings: () => Promise<NotificationSettings>;
  deleteDeviceToken: () => Promise<boolean>;
  handleNotificationPress: (data: NotificationData) => void;
  subscribeToPublicTopic: () => Promise<boolean>;
}
