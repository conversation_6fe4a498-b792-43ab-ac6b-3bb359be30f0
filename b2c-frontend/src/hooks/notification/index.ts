import { useRef } from 'react';
import { Platform, AppState } from 'react-native';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import notifee, {
  AndroidImportance,
  EventType,
  type Notification,
  type AuthorizationStatus,
} from '@notifee/react-native';
import { getApp } from '@react-native-firebase/app';
import {
  getMessaging,
  getToken,
  onMessage,
  setBackgroundMessageHandler,
  onNotificationOpenedApp,
  getInitialNotification,
  subscribeToTopic,
  type FirebaseMessagingTypes,
} from '@react-native-firebase/messaging';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { BottomTabNavigationI, RootDrawerParamListI } from '@/src/navigation/types';
import FirebaseService from '@/src/services/firebase';
import useStorage from '@/src/hooks/storage';
import type {
  NotificationChannel,
  LocalNotificationOptions,
  NotificationHookReturn,
  NotificationData,
  NotificationSettings,
} from './types';

const useNotification = (): NotificationHookReturn => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const currentUser = useSelector(selectCurrentUser);
  const messagingRef = useRef<FirebaseMessagingTypes.Module | null>(null);
  const { getStorage, setStorage } = useStorage();
  const displayedNotifications = useRef<Set<string>>(new Set());
  const setupCompleted = useRef(false);
  const processedRemoteMessages = useRef<Set<string>>(new Set());

  const initFirebaseIfNeeded = async (): Promise<void> => {
    await FirebaseService.getInstance();
    const app = getApp();
    messagingRef.current = getMessaging(app);
  };

  const createNotificationChannels = async (
    channels: NotificationChannel[] = [],
  ): Promise<void> => {
    const defaultChannels: NotificationChannel[] = [
      {
        id: 'default',
        name: 'Default Notifications',
        description: 'General app notifications',
        importance: AndroidImportance.HIGH,
      },
      {
        id: 'high_priority',
        name: 'Important Notifications',
        description: 'High priority notifications',
        importance: AndroidImportance.HIGH,
      },
      ...channels,
    ];
    await Promise.all(
      defaultChannels.map((channel) =>
        notifee.createChannel({
          id: channel.id,
          name: channel.name,
          description: channel.description,
          importance: channel.importance || AndroidImportance.HIGH,
          vibration: channel.vibration !== false,
          vibrationPattern: channel.vibrationPattern || [300, 500],
          lights: channel.lights !== false,
          lightColor: channel.lightColor || '#FF0000',
          sound: 'default',
        }),
      ),
    );
  };

  const checkNotificationPermissions = async (): Promise<boolean> => {
    if (Platform.OS === 'android') {
      const settings = await notifee.requestPermission();
      return settings.authorizationStatus >= 1;
    }
    const settings = await notifee.getNotificationSettings();
    return settings.authorizationStatus >= 1;
  };

  const getDeviceToken = async (forceRefresh = false): Promise<string | null> => {
    try {
      let deviceToken = forceRefresh ? null : await getStorage('deviceToken');
      if (deviceToken && !forceRefresh) return deviceToken;
      if (!messagingRef.current) await initFirebaseIfNeeded();
      const hasPermission = await checkNotificationPermissions();
      if (!hasPermission) {
        return null;
      }
      if (Platform.OS === 'ios' && !messagingRef.current!.isDeviceRegisteredForRemoteMessages) {
        await messagingRef.current!.registerDeviceForRemoteMessages();
      }
      deviceToken = await getToken(messagingRef.current!);
      if (deviceToken) await setStorage('deviceToken', deviceToken);
      return deviceToken;
    } catch (error) {
      console.error('Error getting device token:', error);
      return null;
    }
  };

  const deleteDeviceToken = async (): Promise<boolean> => {
    try {
      await setStorage('deviceToken', '');
      return true;
    } catch {
      return false;
    }
  };

  const subscribeToPublicTopic = async (): Promise<boolean> => {
    try {
      if (!messagingRef.current) await initFirebaseIfNeeded();
      await subscribeToTopic(messagingRef.current!, 'public');
      return true;
    } catch (error) {
      console.error('Error subscribing to public topic:', error);
      return false;
    }
  };

  const handleNotificationPress = (data: NotificationData): void => {
    if (!data) return;

    if (data.commentId || data.parentCommentId || data.postId) {
      navigation.navigate('HomeStack', {
        screen: 'Comment',
        params: { postId: data.postId!, type: 'USER_POST' },
      });
    } else if (data.actorProfileId) {
      const profileParams = { profileId: data.actorProfileId, fromTabPress: false };
      const isCurrentUser = currentUser.profileId === data.actorProfileId;

      if (isCurrentUser) {
        drawerNavigation.navigate('ProfileStack', {
          screen: 'UserProfile',
          params: profileParams,
        });
      } else {
        navigation.navigate('HomeStack', {
          screen: 'OtherUserProfile',
          params: profileParams,
        });
      }
    }
  };

  const generateNotificationKey = (options: LocalNotificationOptions): string => {
    const dataString = options.data ? JSON.stringify(options.data) : '';
    return `${options.title || ''}-${options.body || ''}-${dataString}`.replace(/\s+/g, '');
  };

  const generateRemoteMessageKey = (remoteMessage: any): string => {
    const messageId = remoteMessage.messageId || remoteMessage.fcmMessageId;
    if (messageId) return messageId;

    const { notification, data } = remoteMessage;
    const title = notification?.title || '';
    const body = notification?.body || '';
    const dataString = data ? JSON.stringify(data) : '';
    return `${title}-${body}-${dataString}-${Date.now()}`.replace(/\s+/g, '');
  };

  const displayNotification = async (options: LocalNotificationOptions): Promise<string> => {
    const notificationKey = generateNotificationKey(options);
    if (displayedNotifications.current.has(notificationKey)) {
      return '';
    }
    displayedNotifications.current.add(notificationKey);
    setTimeout(() => {
      displayedNotifications.current.delete(notificationKey);
    }, 10000);

    const notification: Notification = {
      title: options.title,
      body: options.body,
      data: options.data || {},
    };

    if (Platform.OS === 'android') {
      notification.android = {
        channelId: options.android?.channelId || 'default',
        importance: options.android?.importance || AndroidImportance.HIGH,
        smallIcon: options.android?.smallIcon || 'ic_stat_transparent',
        color: '#FF0000',
        vibrationPattern: [300, 500],
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
        showTimestamp: true,
        autoCancel: true,
      };
    }

    if (Platform.OS === 'ios') {
      notification.ios = {
        sound: 'default',
        attachments: [],
      };
    }

    try {
      return await notifee.displayNotification(notification);
    } catch (error) {
      console.error('Error displaying notification:', error);
      return '';
    }
  };

  const scheduleNotification = async (options: LocalNotificationOptions): Promise<string> => {
    const notification: Notification = {
      title: options.title,
      body: options.body,
      data: options.data || {},
    };

    if (Platform.OS === 'android') {
      notification.android = {
        channelId: 'default',
        importance: AndroidImportance.HIGH,
        smallIcon: 'ic_stat_transparent',
        color: '#FF0000',
        vibrationPattern: [300, 500],
        autoCancel: true,
      };
    }

    if (!options.scheduleDate) {
      return await notifee.displayNotification(notification);
    }

    const trigger = {
      type: 0 as const,
      timestamp: options.scheduleDate.getTime(),
    };

    return await notifee.createTriggerNotification(notification, trigger);
  };

  const cancelNotification = async (notificationId: string): Promise<void> => {
    await notifee.cancelNotification(notificationId);
  };

  const cancelAllNotifications = async (): Promise<void> => {
    await notifee.cancelAllNotifications();
  };

  const getNotificationSettings = async (): Promise<NotificationSettings> => {
    const settings = await notifee.getNotificationSettings();
    return {
      authorizationStatus: settings.authorizationStatus as AuthorizationStatus,
      alert: settings.ios?.alert === 1,
      badge: settings.ios?.badge === 1,
      sound: settings.ios?.sound === 1,
      carPlay: settings.ios?.carPlay === 1,
      lockScreen: settings.ios?.lockScreen === 1,
      notificationCenter: settings.ios?.notificationCenter === 1,
      criticalAlert: settings.ios?.criticalAlert === 1,
      announcement: settings.ios?.announcement === 1,
    };
  };

  const setupNotification = async (channels?: NotificationChannel[]): Promise<boolean> => {
    if (setupCompleted.current) {
      return true;
    }
    try {
      await initFirebaseIfNeeded();
      await createNotificationChannels(channels);
      await checkNotificationPermissions();
      await subscribeToPublicTopic();

      if (!messagingRef.current) return false;

      onMessage(messagingRef.current, async (remoteMessage) => {
        const messageKey = generateRemoteMessageKey(remoteMessage);
        if (processedRemoteMessages.current.has(messageKey)) {
          return;
        }
        processedRemoteMessages.current.add(messageKey);

        setTimeout(() => {
          processedRemoteMessages.current.delete(messageKey);
        }, 30000);

        const { notification, data } = remoteMessage;
        if (notification && AppState.currentState === 'active') {
          await displayNotification({
            title: notification.title || 'New Notification',
            body: notification.body || 'You have a new message',
            data: data || {},
          });
        }
      });

      setBackgroundMessageHandler(messagingRef.current, async (_remoteMessage) => {
        return Promise.resolve();
      });

      onNotificationOpenedApp(messagingRef.current, (remoteMessage) => {
        handleNotificationPress(remoteMessage.data as NotificationData);
      });

      getInitialNotification(messagingRef.current).then((remoteMessage) => {
        if (remoteMessage) {
          handleNotificationPress(remoteMessage.data as NotificationData);
        }
      });

      notifee.onForegroundEvent(({ type, detail }) => {
        if (type === EventType.PRESS && detail.notification?.data) {
          handleNotificationPress(detail.notification.data as NotificationData);
        }
      });

      setupCompleted.current = true;
      return true;
    } catch (_error) {
      return false;
    }
  };

  return {
    setupNotification,
    getDeviceToken,
    checkNotificationPermissions,
    displayNotification,
    scheduleNotification,
    cancelNotification,
    cancelAllNotifications,
    createNotificationChannels,
    getNotificationSettings,
    deleteDeviceToken,
    handleNotificationPress,
    subscribeToPublicTopic,
  };
};

export default useNotification;
