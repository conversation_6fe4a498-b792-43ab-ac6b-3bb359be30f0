import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path, SvgProps } from 'react-native-svg';

interface IconProps extends SvgProps {
  width?: number;
  height?: number;
  primaryColor?: string;
  secondaryColor?: string;
}

const LeaderBoardIcon: React.FC<IconProps> = ({
  width = 4,
  height = 4,
  primaryColor = '#613EEA',
  secondaryColor = '#E6C834',
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 34 34"
      fill="none"
      {...props}
    >
      <Path
        d="M29.749 18.417h-7.084v-4.25a1.417 1.417 0 00-1.416-1.417h-8.5a1.417 1.417 0 00-1.417 1.417v7.083H4.249a1.417 1.417 0 00-1.417 1.417v7.083a1.417 1.417 0 001.417 1.417h25.5a1.416 1.416 0 001.416-1.417v-9.917a1.417 1.417 0 00-1.416-1.416z"
        fill={primaryColor}
      />
      <Path
        d="M18.316 9.704c-.232.003-.46-.05-.666-.156l-.652-.34-.652.34a1.417 1.417 0 01-2.054-1.501l.128-.723-.538-.51a1.416 1.416 0 01.793-2.422l.722-.142.326-.666a1.417 1.417 0 012.55 0l.326.666.723.1a1.417 1.417 0 01.793 2.422l-.538.51.127.722a1.416 1.416 0 01-.553 1.417c-.24.18-.534.28-.835.283z"
        fill={secondaryColor}
      />
    </Svg>
  );
};

export default LeaderBoardIcon;
