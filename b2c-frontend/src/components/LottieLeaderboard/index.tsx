import React, { useRef, useEffect } from 'react';
import { View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import LottieView from 'lottie-react-native';

type LottieLeaderboardPropsI = {
  width?: number;
  height?: number;
  autoPlay?: boolean;
  loop?: boolean;
  speed?: number;
};

const LottieLeaderboard: React.FC<LottieLeaderboardPropsI> = ({
  width = 4,
  height = 4,
  autoPlay = true,
  loop = true,
  speed = 1,
}) => {
  const animationRef = useRef<LottieView>(null);

  useEffect(() => {
    if (autoPlay && animationRef.current) {
      animationRef.current.play();
    }
  }, [autoPlay]);

  return (
    <View
      style={{
        width: RFPercentage(width),
        height: RFPercentage(height),
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <LottieView
        ref={animationRef}
        source={require('@/src/assets/animations/leaderboard.json')}
        autoPlay={autoPlay}
        loop={loop}
        speed={speed}
        style={{
          width: '100%',
          height: '100%',
        }}
        resizeMode="contain"
      />
    </View>
  );
};

export default LottieLeaderboard;
