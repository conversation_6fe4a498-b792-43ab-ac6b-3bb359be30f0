import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export type AnnouncementItemI = {
  announcementId: string;
  cursorId: number;
  title: string;
  description: string;
  latitude?: number;
  longitude?: number;
  addressRawData: {
    id: string;
    text: string;
  };
  cityId: string | null;
  cityRawDataId: string | null;
  countryIso2: string | null;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  totalAttendees: number;
  createdAt: string;
  updatedAt: string;
  isRSVPed: boolean;
  city?: SearchResultI;
  topAttendeesRaw:
    | {
        id: string;
        name: string;
        avatar: null | string;
      }[]
    | null;
};

export type ProfileItemI = {
  id: string;
  name: string;
  avatar: string | null;
  designation: SearchResultI;
  entity: SearchResultI | null;
};

export interface AnnouncementProps {
  announcement: AnnouncementItemI;
  user: ProfileItemI;
  selfProfileId: string;
  onDelete: (id: string) => void;
}
