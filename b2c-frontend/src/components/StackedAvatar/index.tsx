import { View, Text } from 'react-native';
import UserAvatar from '../UserAvatar';
import { UserI } from './types';

const StackedAvatar = ({
  users,
  size = 20,
  overlap = 10,
  maxDisplayed = 5,
  showCount = true,
}: {
  users: UserI[] | null;
  size?: number;
  overlap?: number;
  maxDisplayed?: number;
  showCount?: boolean;
}) => {
  if (!users || users.length === 0) return null;

  const displayCount = Math.min(users.length, maxDisplayed);
  const remainingCount = users.length - displayCount;
  const displayedUsers = users.slice(0, displayCount);
  const showRemainingCount = showCount && remainingCount > 0;

  return (
    <View className="flex-row items-center" style={{ height: size }}>
      {displayedUsers.map((user, index) => (
        <View
          key={`${user.id}-${index}`}
          className="rounded-full border-2 border-white"
          style={{
            marginLeft: index === 0 ? 0 : -overlap,
            zIndex: displayCount - index,
            width: size,
            height: size,
            backgroundColor: '#f3f4f6',
          }}
        >
          <UserAvatar avatarUri={user.avatar} name={user.name} width={size} height={size} />
        </View>
      ))}

      {showRemainingCount && (
        <View
          className="rounded-full border-2 border-white items-center justify-center bg-gray-300"
          style={{
            marginLeft: -overlap,
            zIndex: 0,
            width: size,
            height: size,
          }}
        >
          <Text className="text-gray-600 font-medium" style={{ fontSize: size * 0.35 }}>
            +{remainingCount}
          </Text>
        </View>
      )}
    </View>
  );
};

export default StackedAvatar;
