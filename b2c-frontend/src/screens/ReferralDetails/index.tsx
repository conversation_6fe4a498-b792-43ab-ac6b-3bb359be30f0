import { useState, useRef } from 'react';
import { View, Text, ScrollView, TextInput, Pressable } from 'react-native';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import Clipboard from '@react-native-clipboard/clipboard';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import ReferralTermsModal from '@/src/components/ReferralTerms';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { RootDrawerParamListI } from '@/src/navigation/types';

const ReferralDetailsScreen = () => {
  const currentUser = useSelector(selectCurrentUser);
  const navigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const [modalVisible, setModalVisible] = useState(false);
  const [copied, setCopied] = useState(false);
  const referralLink = `https://network.navicater.com/referral/${currentUser?.referralCode || ''}`;
  const textInputRef = useRef(null);

  const handleCopy = async () => {
    Clipboard.setString(referralLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <SafeArea>
      <BackButton onBack={() => navigation.goBack()} />
      <ScrollView className="flex-1 bg-white">
        <View className="px-6 py-5">
          <Text className="text-2xl font-bold text-center mb-8 text-[#1A2B40]">
            Earn points by referring friends
          </Text>
          <View className="flex-row items-center bg-gray-100 rounded-lg p-1 mb-6">
            <TextInput
              ref={textInputRef}
              value={referralLink}
              editable={false}
              className="flex-1 text-gray-700 px-2 py-1"
            />
            <Pressable onPress={handleCopy} className="bg-gray-200 px-3 py-2 rounded-md mr-1">
              <Text className="text-gray-800 font-medium">{copied ? 'Copied!' : 'Copy'}</Text>
            </Pressable>
          </View>

          <Pressable
            onPress={() => setModalVisible(true)}
            className="bg-[#FFD700] px-4 py-6 rounded-xl mb-6 flex-row items-center space-x-3"
          >
            <View className="flex-1">
              <Text className="text-base font-bold text-[#1A2B40]">
                Earn 100+ points and unlock a special gift from NAVICATER
              </Text>
              <Text className="text-xs text-gray-700 mt-3">
                *Limited time offer - Tap for details & conditions
              </Text>
            </View>
          </Pressable>

          <Text className="text-base font-semibold mb-4 text-[#1A2B40]">Points earned</Text>
          <View className="h-24 bg-gray-100 rounded-md"></View>
        </View>

        <ReferralTermsModal
          isVisible={modalVisible}
          onClose={() => setModalVisible(!modalVisible)}
        />
      </ScrollView>
    </SafeArea>
  );
};

export default ReferralDetailsScreen;
