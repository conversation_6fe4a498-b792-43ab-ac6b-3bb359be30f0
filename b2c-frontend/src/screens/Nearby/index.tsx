/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ReactNode, useState } from 'react';
import { Pressable, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import { Tab } from '@/src/components/Tabs/types';
import { NearbyStackParamListI } from '@/src/navigation/types';
import Settings from '@/src/assets/svgs/Settings';
import { Announcements } from './components/Announcements';
import People from './components/People';

const tabs: Tab[] = [
  {
    id: 'people',
    label: 'People',
  },
  {
    id: 'announcement',
    label: 'Announcements',
  },
];
const tabComponents: { [key: string]: ReactNode } = {
  people: <People />,
  announcement: <Announcements />,
};

export const NearbyScreen = () => {
  const navigation = useNavigation<StackNavigationProp<NearbyStackParamListI>>();
  const [activeTab, setActiveTab] = useState('people');

  const handleBack = () => {
    navigation.goBack();
  };

  const handleSettingsClick = () => {
    navigation.navigate('NearbySettings');
  };

  return (
    <SafeArea>
      <View className="flex-row items-center justify-between">
        <BackButton
          onBack={handleBack}
          label="Nearby"
          labelClassname="text-lg leading-6 font-medium"
        />
        <Pressable onPress={handleSettingsClick} className="pr-2">
          <Settings />
        </Pressable>
      </View>
      <View className="flex-1">
        <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
        {tabComponents[activeTab]}
      </View>
    </SafeArea>
  );
};
