import { useEffect, useState } from 'react';
import { Platform, PermissionsAndroid } from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { formatLatitude, formatLongitude } from '@/src/utilities/location/coordinates';
import { showToast } from '@/src/utilities/toast';
import { fetchAnonymousStatus, toggleAnonymousAPI } from '@/src/networks/nearby/anonymous';
import { fetchPeopleNearby } from '@/src/networks/nearby/fetchPeople';
import type { fetchPeopleResultDataI } from '@/src/networks/nearby/types';
import type { NearbyListItem } from './types';

export const useNearbyPeople = () => {
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [nearbyProfiles, setNearbyProfiles] = useState<NearbyListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [cursorId, setCursorId] = useState<number | null>(null);
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [cooldownEnd, setCooldownEnd] = useState<string | null>(null);

  useEffect(() => {
    const getCurrentLocation = () => {
      Geolocation.getCurrentPosition(
        (position) => {
          setUserLocation([
            formatLongitude(position.coords.longitude),
            formatLatitude(position.coords.latitude),
          ]);
        },
        (err) => {
          setError(err.message);
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
      );
    };

    const requestLocationPermission = async () => {
      if (Platform.OS === 'android') {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
              title: 'Location Permission',
              message: 'This app needs access to your location',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            getCurrentLocation();
          } else {
            setError('Location permission denied');
          }
        } catch (err) {
          setError('Error requesting location permission');
        }
      } else if (Platform.OS === 'ios') {
        const status = await Geolocation.requestAuthorization('whenInUse');

        if (status === 'granted') {
          getCurrentLocation();
        } else {
          setError('Location permission denied on iOS');
        }
      } else {
        getCurrentLocation();
      }
    };

    requestLocationPermission();
  }, []);

  useEffect(() => {
    const fetchPeople = async () => {
      if (!userLocation || userLocation.length < 2) {
        return;
      }
      try {
        setLoading(true);
        const anonymousStatus = await fetchAnonymousStatus();
        setIsAnonymous(anonymousStatus);
        const users = await fetchPeopleNearby(
          { cursorId: null, pageSize: 10 },
          { longitude: userLocation[0], latitude: userLocation[1] },
        );
        if (users.data) {
          const profiles = parseProfiles(users.data);
          setNearbyProfiles(profiles);
          if (users.nextCursorId) {
            setCursorId(Number(users.nextCursorId));
          }
        }
      } catch (error) {
        console.log('Error fetching nearby people:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPeople();
  }, [userLocation, isAnonymous]);

  useEffect(() => {
    const loadCooldown = async () => {
      try {
        const savedCooldown = await AsyncStorage.getItem('@anonymous_cooldown');
        if (savedCooldown) {
          setCooldownEnd(savedCooldown);
        }
      } catch (error) {
        console.error('Failed to load cooldown', error);
      }
    };
    loadCooldown();
  }, []);

  const calculateCameraBounds = () => {
    return {};
  };

  const loadMoreProfiles = async () => {
    if (!cursorId || !userLocation || userLocation.length < 2 || loading) return;

    try {
      setLoading(true);
      const users = await fetchPeopleNearby(
        { cursorId, pageSize: 10 },
        { longitude: userLocation[0], latitude: userLocation[1] },
      );
      if (users.data) {
        const newProfiles = parseProfiles(users.data);
        setNearbyProfiles((prev) => [...prev, ...newProfiles]);
        if (users.nextCursorId) {
          setCursorId(Number(users.nextCursorId));
        }
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to load more profiles',
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleAnonymous = async () => {
    try {
      if (isAnonymous && cooldownEnd) {
        const now = new Date();
        const endTime = new Date(cooldownEnd);
        if (now < endTime) return;
      }

      await toggleAnonymousAPI();

      if (!isAnonymous) {
        const endTime = new Date();
        endTime.setHours(endTime.getHours() + 24);
        const endTimeString = endTime.toISOString();

        await AsyncStorage.setItem('@anonymous_cooldown', endTimeString);
        setCooldownEnd(endTimeString);
      } else {
        await AsyncStorage.removeItem('@anonymous_cooldown');
        setCooldownEnd(null);
      }

      setIsAnonymous(!isAnonymous);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Failed to update anonymity status',
      });
    }
  };

  return {
    userLocation,
    error,
    nearbyProfiles,
    calculateCameraBounds,
    loading,
    setLoading,
    loadMoreProfiles,
    isAnonymous,
    toggleAnonymous,
    cooldownEnd,
  };
};

const parseProfiles = (users: fetchPeopleResultDataI[]) => {
  if (!users || !Array.isArray(users)) {
    return [];
  }
  return users.map((user) => {
    const distance = Math.floor(user.distanceInMeters / 1000);
    return {
      Profile: {
        id: user.id,
        avatar: user.avatar || null,
        name: user.name,
        designation: user.designation,
        entity: user.entity,
      },
      latitude: user.latitude,
      longitude: user.longitude,
      distanceInMetres: distance,
      cursorId: user.cursorId,
    };
  });
};
