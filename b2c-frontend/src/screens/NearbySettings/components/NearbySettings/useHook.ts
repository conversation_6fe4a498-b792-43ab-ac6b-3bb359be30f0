import { useEffect, useState } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Config from 'react-native-config';
import Geolocation from 'react-native-geolocation-service';
import { useForm, useFieldArray } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectNearbyFilters } from '@/src/redux/selectors/announcement';
import {
  clearOtherLocation,
  setNearbyRadius,
  setOtherLocation,
  setSelfLocation,
} from '@/src/redux/slices/announcement/announcementSlice';
import type { AppDispatch } from '@/src/redux/store';
import { debounceAsync } from '@/src/utilities/search/debounce';
import { showToast } from '@/src/utilities/toast';
import type { MapboxSuggestion, NearbySettingsFormFiltersI } from './types';

export const useNearbySettings = () => {
  const nearbyFilters = useSelector(selectNearbyFilters);
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();

  const [selfLocationSuggestions, setSelfLocationSuggestions] = useState<MapboxSuggestion[]>([]);
  const [otherLocationSuggestions, setOtherLocationSuggestions] = useState<MapboxSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [isOtherLocationSuggestionFetching, setIsOtherLocationSuggestionFetching] = useState(false);
  const [isSelfLocationSuggestionFetching, setIsSelfLocationSuggestionFetching] = useState(false);

  const methods = useForm<NearbySettingsFormFiltersI>({
    mode: 'onChange',
    defaultValues: {
      locations: [
        {
          id: 'self',
          type: 'self',
          name: nearbyFilters.selfLocation.name || '',
          coords: [nearbyFilters.selfLocation.coords.longitude,nearbyFilters.selfLocation.coords.longitude],
        },
      ],
      radius: nearbyFilters.radius || 0,
    },
  });

  const isSelfLocationValid = (
    nearbyFilters.selfLocation?.coords?.latitude !== undefined &&
    nearbyFilters.selfLocation?.coords?.longitude !== undefined &&
    !(nearbyFilters.selfLocation.coords.latitude === 0 && 
        nearbyFilters.selfLocation.coords.longitude === 0)
    )

const isOtherLocationValid = (
    nearbyFilters.otherLocation?.coords?.latitude !== undefined &&
    nearbyFilters.otherLocation?.coords?.longitude !== undefined &&
    !(nearbyFilters.otherLocation.coords.latitude === 0 && 
        nearbyFilters.otherLocation.coords.longitude === 0)
    )

  const { control, setValue, formState } = methods;
  const { fields, append, remove } = useFieldArray({ control, name: 'locations' });

  const isOtherLocationAdded = fields.some((field) => field.type === 'other');

  const fetchLocationSuggestions = async (query: string): Promise<MapboxSuggestion[]> => {
    if (!query) return [];
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          query,
        )}.json?access_token=${Config.MAPBOX_ACCESS_TOKEN}`,
      );
      const data = await response.json();
      return data.features || [];
    } catch (error) {
      console.error('Failed to fetch location suggestions:', error);
      return [];
    }
  };

  const debouncedSelfLocationSearch = debounceAsync(async (query: any) => {
    if (query.length > 2) {
      setIsSelfLocationSuggestionFetching(true);
      const results = await fetchLocationSuggestions(query);
      setSelfLocationSuggestions(results);
      setIsSelfLocationSuggestionFetching(false);
    } else {
      setSelfLocationSuggestions([]);
      setIsSelfLocationSuggestionFetching(false);
    }
  }, 400);

  const debouncedOtherLocationSearch = debounceAsync(async (query: any) => {
    if (query.length > 2) {
      setIsOtherLocationSuggestionFetching(true);
      const results = await fetchLocationSuggestions(query);
      setOtherLocationSuggestions(results);
      setIsOtherLocationSuggestionFetching(false);
    } else {
      setOtherLocationSuggestions([]);
      setIsOtherLocationSuggestionFetching(false);
    }
  }, 400);

  useEffect(() => {
    const getCurrentLocation = () => {
        if(isSelfLocationValid) {
            setValue('locations.0.name', nearbyFilters.selfLocation.name);
            setValue('locations.0.coords', [nearbyFilters.selfLocation.coords.longitude, nearbyFilters.selfLocation.coords.latitude]);
            
            if(isOtherLocationValid) {
                setValue('locations.1.name', nearbyFilters.otherLocation.name);
                setValue('locations.1.coords', [nearbyFilters.otherLocation.coords.longitude, nearbyFilters.otherLocation.coords.latitude]);
            }
            
            return;
        }
      Geolocation.getCurrentPosition(
        async (position) => {
          const { longitude, latitude } = position.coords;
          try {
            const response = await fetch(
              `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${Config.MAPBOX_ACCESS_TOKEN}`,
            );
            const data = await response.json();
            const addressItem = data.features?.find((item: MapboxSuggestion) =>
              item.id.startsWith('address.'),
            );
            const placeName =
              addressItem?.place_name || data.features?.[0]?.place_name || 'Current Location';
            dispatch(setSelfLocation({coords:{latitude,longitude},name:placeName}))
            setValue('locations.0.name', placeName, { shouldDirty: true });
            setValue('locations.0.coords', [longitude, latitude], { shouldDirty: true });
          } catch (err) {
            showToast({ type: 'error', message: 'Failed to get location details' });
          }
        },
        (err) => {
          showToast({ type: 'error', message: err.message });
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 },
      );
    };

    const requestLocationPermission = async () => {
      if (Platform.OS === 'android') {
        try {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
              title: 'Location Permission',
              message: 'This app needs access to your location',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            getCurrentLocation();
          } else {
            showToast({ type: 'error', message: 'Location permission denied' });
          }
        } catch (err) {
          showToast({ type: 'error', message: 'Failed to request location permission' });
        }
      } else if (Platform.OS === 'ios') {
        const status = await Geolocation.requestAuthorization('whenInUse');
        if (status === 'granted') {
          getCurrentLocation();
        } else {
          showToast({ type: 'error', message: 'Location permission denied' });
        }
      } else {
        getCurrentLocation();
      }
    };

    requestLocationPermission();
  }, [dispatch, setValue]);

  const handleAddOtherLocation = () => {
    if (!isOtherLocationAdded) {
      append({ id: 'other', type: 'other', name: nearbyFilters.otherLocation.name || '', coords: [nearbyFilters.otherLocation.coords.longitude,nearbyFilters.otherLocation.coords.latitude] });
    //   dispatch(setOtherLocation([0, 0]));
    }
  };

  const handleDeleteOtherLocation = () => {
    const otherLocationIndex = fields.findIndex((field) => field.type === 'other');
    if (otherLocationIndex !== -1) {
      remove(otherLocationIndex);
      dispatch(clearOtherLocation());
      setOtherLocationSuggestions([]);
    }
  };

  const handleSelfLocationChange = (text: string) => {
    setValue('locations.0.name', text, { shouldDirty: true });
    debouncedSelfLocationSearch(text);
  };

  const handleOtherLocationChange = (text: string) => {
    const otherLocationIndex = fields.findIndex((field) => field.type === 'other');
    if (otherLocationIndex !== -1) {
      setValue(`locations.${otherLocationIndex}.name`, text, { shouldDirty: true });
      debouncedOtherLocationSearch(text);
    }
  };

  const handleSelectSuggestion = (suggestion: MapboxSuggestion, fieldType: 'self' | 'other') => {
    const [longitude, latitude] = suggestion.center;
    const index = fieldType === 'self' ? 0 : fields.findIndex((field) => field.type === 'other');
    if (index !== -1) {
      setValue(`locations.${index}.name`, suggestion.place_name, { shouldDirty: true });
      setValue(`locations.${index}.coords`, [longitude, latitude], { shouldDirty: true });
      if (fieldType === 'self') {
        dispatch(setSelfLocation({
            coords:{
                longitude:suggestion.center[0],
                latitude:suggestion.center[1]
            },
            name:suggestion.place_name
        }));
        setSelfLocationSuggestions([]);
      } else {
        dispatch(setOtherLocation({
            coords:{
                longitude:suggestion.center[0],
                latitude:suggestion.center[1]
            },
            name:suggestion.place_name
        }));
        setOtherLocationSuggestions([]);
      }
    }
  };

  const onSubmit = (data: NearbySettingsFormFiltersI) => {
    setLoading(true);
    setTimeout(() => {
    //   const selfLoc = data.locations.find((loc) => loc.type === 'self');
    //   const otherLoc = data.locations.find((loc) => loc.type === 'other');
    //   if (selfLoc?.coords) {
    //     dispatch(setSelfLocation(selfLoc.coords));
    //   }
    //   if (otherLoc?.coords) {
    //     dispatch(setOtherLocation(otherLoc.coords));
    //   } else {
        // dispatch(clearOtherLocation());
    //   }
      dispatch(setNearbyRadius(data.radius));
      setLoading(false);
      navigation.goBack();
    }, 200);
  };

  return {
    methods,
    hasChanges: formState.isDirty,
    handleAddOtherLocation,
    isOtherLocationAdded,
    handleDeleteOtherLocation,
    loading,
    handleSelfLocationChange,
    handleOtherLocationChange,
    selfLocationSuggestions,
    otherLocationSuggestions,
    isSelfLocationSuggestionFetching,
    isOtherLocationSuggestionFetching,
    handleSelectSuggestion,
    setSelfLocationSuggestions,
    setOtherLocationSuggestions,
    onSubmit,
    fields,
    isOtherLocationValid
  };
};
