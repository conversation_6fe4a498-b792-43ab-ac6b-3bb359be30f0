import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import BackButton from '@/src/components/BackButton';
import TextInput from '@/src/components/TextInput';
import AddItem from '@/src/assets/svgs/AddItem';
import TrashBin from '@/src/assets/svgs/TrashBin';
import InformationText from '../InformationText';
import type { EditNearbySettingsPropsI, MapboxSuggestion } from './types';
import { useNearbySettings } from './useHook';

const EditNearbySettings = ({ onBack }: EditNearbySettingsPropsI) => {
  const {
    hasChanges,
    methods,
    handleAddOtherLocation,
    isOtherLocationAdded,
    handleDeleteOtherLocation,
    loading,
    handleOtherLocationChange,
    handleSelfLocationChange,
    selfLocationSuggestions,
    otherLocationSuggestions,
    isOtherLocationSuggestionFetching,
    isSelfLocationSuggestionFetching,
    handleSelectSuggestion,
    onSubmit,
    fields,
    isOtherLocationValid
  } = useNearbySettings();

  const { control, handleSubmit } = methods;
  const selfLocationField = fields.find((field) => field.type === 'self');
  const otherLocationField = fields.find((field) => field.type === 'other');

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="flex-row items-center justify-between py-4">
        <BackButton onBack={onBack} label="Nearby Settings" />
        <Pressable onPress={handleSubmit(onSubmit)} disabled={!hasChanges} className="pr-2">
          <Text
            className={`text-lg font-medium ${!hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
          >
            {loading ? 'Applying...' : 'Apply'}
          </Text>
        </Pressable>
      </View>
      <View className="gap-4 px-4">
        <InformationText />
        {selfLocationField && (
          <Controller
            control={control}
            name={`locations.${fields.indexOf(selfLocationField)}.name`}
            rules={{ required: 'Location is required' }}
            disabled
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <View>
                <TextInput
                  label="Location"
                  value={value}
                  onChangeText={(text) => {
                    onChange(text);
                    handleSelfLocationChange(text);
                  }}
                  error={error?.message}
                />
                {isSelfLocationSuggestionFetching && <ActivityIndicator size="small" />}
                {selfLocationSuggestions.length > 0 && (
                  <View className="mt-2 rounded-lg border border-gray-200 bg-white">
                    {selfLocationSuggestions.map((suggestion: MapboxSuggestion) => (
                      <Pressable
                        key={suggestion.id}
                        className="p-3 border-b border-gray-100"
                        onPress={() => {
                          handleSelectSuggestion(suggestion, 'self');
                        }}
                      >
                        <Text>{suggestion.place_name}</Text>
                      </Pressable>
                    ))}
                  </View>
                )}
              </View>
            )}
          />
        )}
        <Pressable onPress={handleAddOtherLocation}>
          <AddItem color={isOtherLocationAdded ? 'gray' : '#448600'} />
        </Pressable>
        {isOtherLocationAdded && otherLocationField && (
          <View>
            <Controller
              control={control}
              name={`locations.${fields.indexOf(otherLocationField)}.name`}
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <View>
                  <TextInput
                    label="Other Location"
                    value={value}
                    onChangeText={(text) => {
                      onChange(text);
                      handleOtherLocationChange(text);
                    }}
                    error={error?.message}
                  />
                  {isOtherLocationSuggestionFetching && <ActivityIndicator size="small" />}
                  {otherLocationSuggestions.length > 0 && (
                    <View className="mt-2 rounded-lg border border-gray-200 bg-white">
                      {otherLocationSuggestions.map((suggestion: MapboxSuggestion) => (
                        <Pressable
                          key={suggestion.id}
                          className="p-3 border-b border-gray-100"
                          onPress={() => {
                            handleSelectSuggestion(suggestion, 'other');
                          }}
                        >
                          <Text>{suggestion.place_name}</Text>
                        </Pressable>
                      ))}
                    </View>
                  )}
                </View>
              )}
            />
            <View className="flex-row justify-between">
              <View />
              <Pressable className="m-3" onPress={handleDeleteOtherLocation}>
                <TrashBin width={2.5} height={2.5} color="red" />
              </Pressable>
            </View>
          </View>
        )}
        <Controller
          control={control}
          name="radius"
          rules={{
            required: 'Radius is required',
            validate: (value) => (value > 0 ? true : 'Radius must be greater than 0'),
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <TextInput
              label="Radius(km)"
              value={value?.toString()}
              onChangeText={(text) => {
                const numValue = text ? Number.parseFloat(text) : 0;
                onChange(numValue);
              }}
              keyboardType="numeric"
              error={error?.message}
            />
          )}
        />
      </View>
    </ScrollView>
  );
};

export default EditNearbySettings;
