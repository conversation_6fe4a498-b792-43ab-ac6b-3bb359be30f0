import { prismaPG } from '@config/db';
import { randomUUID } from 'node:crypto';

export const ReferralCodeModule = {
  findByProfileId: async (profileId: string) => {
    return prismaPG.referralCodes.findFirst({
      where: { profileId },
    });
  },

  findByCode: async (code: string) => {
    return prismaPG.referralCodes.findFirst({
      where: { code },
    });
  },

  create: async (profileId: string, code: string) => {
    return prismaPG.referralCodes.create({
      data: {
        profileId,
        code,
      },
    });
  },

  ensureReferralCode: async (profileId: string) => {
    const existing = await ReferralCodeModule.findByProfileId(profileId);
    if (existing) return existing.code;

    let code = '';
    let isUnique = false;

    while (!isUnique) {
      code = randomUUID().toString();
      const found = await ReferralCodeModule.findByCode(code);
      if (!found) isUnique = true;
    }

    return (await ReferralCodeModule.create(profileId, code)).code;
  },
};
