import { EntityNameR } from '@consts/common/regex/regex';
import { z } from 'zod';
import { PaginationSchema, UUIDSchema } from '../common/common';
import { EntityTypeE } from '@consts/company/entity';
import { DBDataTypeE } from '@consts/common/data';

export const EntityOptionsFetchSchema = PaginationSchema.extend({
  search: z.string().min(1).max(100).regex(EntityNameR),
});
export type EntityModuleFetchParamsI = z.infer<typeof EntityOptionsFetchSchema>;
export const EntityNameSchema = z.object({
  name: z.string().min(2).max(100).regex(EntityNameR),
  type: EntityTypeE,
  website: z.string().url().optional(),
});
export type EntityNameI = z.infer<typeof EntityNameSchema>;

export const EntityIdClientSchema = z.object({
  id: UUIDSchema,
  dataType: DBDataTypeE,
});
export type EntityIdClientI = z.infer<typeof EntityIdClientSchema>;

