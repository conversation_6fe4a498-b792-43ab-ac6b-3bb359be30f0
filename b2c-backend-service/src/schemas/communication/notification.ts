import { VoteTypeE } from '@consts/forum';
import { KafkaTopicE } from '@consts/kafka';
import { NotificationScreenE, NotificationTopicE, NotificationTypeE } from '@consts/notification/notification';
import { ObjectIdSchema, UUIDSchema } from '@schemas/common/common';
import { z } from 'zod';

export const ActorProfileNameSchema = z
  .string()
  .trim()
  .transform((data) => data?.split(' ')?.[0]?.substring(0, 15) || 'Unknown');
export const PostTextSchema = z
  .string()
  .trim()
  .transform((data) => `${data?.length > 20 ? `${data?.substring(0, 16)}...` : data}`);

export const NotificationActorCommonSchema = z.object({
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
});
export type NotificationActorCommonI = z.infer<typeof NotificationActorCommonSchema>;

export const NotificationPublicSchema = z.object({
  screen: NotificationScreenE.optional(),
});
export type NotificationPublicI = z.infer<typeof NotificationPublicSchema>;

export const NotificationForumQuestionCommonSchema = NotificationActorCommonSchema.extend({
  questionId: UUIDSchema,
});
export type NotificationForumQuestionCommonI = z.infer<typeof NotificationForumQuestionCommonSchema>;

export const NotificationForumAnswerCommonSchema = NotificationForumQuestionCommonSchema.extend({
  answerId: UUIDSchema,
});
export type NotificationForumAnswerCommonI = z.infer<typeof NotificationForumAnswerCommonSchema>;

export const NotificationForumQuestionLiveSchema = NotificationForumQuestionCommonSchema.extend({
  questionTitle: PostTextSchema,
});
export type NotificationForumQuestionI = z.infer<typeof NotificationForumQuestionLiveSchema>;

export const NotificationForumQuestionsSchema = z.object({
  questionsCount: z.number(),
});
export type NotificationForumQuestionsI = z.infer<typeof NotificationForumQuestionsSchema>;

export const NotificationForumQuestionVoteSchema = NotificationForumQuestionLiveSchema.extend({
  voteType: VoteTypeE,
});
export type NotificationForumQuestionVoteI = z.infer<typeof NotificationForumQuestionVoteSchema>;

export const NotificationForumAnswerSchema = NotificationForumAnswerCommonSchema.extend({
  questionTitle: PostTextSchema,
});
export type NotificationForumAnswerI = z.infer<typeof NotificationForumAnswerSchema>;

export const NotificationForumAnswerVoteSchema = NotificationForumAnswerCommonSchema.extend({
  answerText: PostTextSchema,
});
export type NotificationForumAnswerVoteI = z.infer<typeof NotificationForumAnswerVoteSchema>;

export const NotificationForumQuestionCommentSchema = NotificationForumQuestionLiveSchema.extend({
  commentId: UUIDSchema,
});
export type NotificationForumQuestionCommentI = z.infer<typeof NotificationForumQuestionCommentSchema>;

export const NotificationForumQuestionReplySchema = NotificationForumQuestionCommonSchema.extend({
  commentId: UUIDSchema,
  parentCommentId: UUIDSchema,
});
export type NotificationForumQuestionReplyI = z.infer<typeof NotificationForumQuestionReplySchema>;

export const NotificationForumAnswerCommentSchema = NotificationForumAnswerCommonSchema.extend({
  commentId: UUIDSchema,
  answerText: PostTextSchema,
});
export type NotificationForumAnswerCommentI = z.infer<typeof NotificationForumAnswerCommentSchema>;

export const NotificationForumAnswerReplySchema = NotificationForumAnswerCommonSchema.extend({
  answerText: PostTextSchema,
  commentId: UUIDSchema,
  parentCommentId: UUIDSchema,
});
export type NotificationForumAnswerReplyI = z.infer<typeof NotificationForumAnswerReplySchema>;

export const NotificationForumAnswerVerifiedSchema = NotificationForumAnswerSchema;
export type NotificationForumAnswerVerifiedI = z.infer<typeof NotificationForumAnswerVerifiedSchema>;

export const NotificationPostSchema = NotificationActorCommonSchema.extend({
  postId: UUIDSchema,
});
export type NotificationPostI = z.infer<typeof NotificationPostSchema>;

export const NotificationLikeSchema = NotificationPostSchema.extend({
  type: z.enum([NotificationTypeE.Enum.LIKE]),
  postText: PostTextSchema,
});
export type NotificationLikeI = z.infer<typeof NotificationLikeSchema>;

export const NotificationCommentSchema = NotificationPostSchema.extend({
  type: z.enum([NotificationTypeE.Enum.COMMENT]),
  commentId: UUIDSchema,
  postText: PostTextSchema,
});
export type NotificationCommentI = z.infer<typeof NotificationCommentSchema>;

export const NotificationReplySchema = NotificationPostSchema.extend({
  type: z.enum([NotificationTypeE.Enum.REPLY]),
  commentId: UUIDSchema,
  parentCommentId: UUIDSchema,
  postText: PostTextSchema,
});

export type NotificationReplyI = z.infer<typeof NotificationReplySchema>;

export const NotificationUpdateReadSchema = z.object({
  type: z.enum([NotificationTypeE.Enum.UPDATE_READ]),
  ids: z.array(ObjectIdSchema).min(1),
});
export type NotificationUpdateReadI = z.infer<typeof NotificationUpdateReadSchema>;

export const NotificationFollowerSchema = z.object({
  type: z.enum([NotificationTypeE.Enum.FOLLOWER]),
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
});
export type NotificationFollowerI = z.infer<typeof NotificationFollowerSchema>;

export const NotificationRequestReceivedSchema = z.object({
  type: z.enum([NotificationTypeE.Enum.REQUEST_RECEIVED]),
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
  requestId: UUIDSchema,
});
export type NotificationRequestReceivedI = z.infer<typeof NotificationRequestReceivedSchema>;

export const NotificationRequestAcceptedSchema = NotificationRequestReceivedSchema.extend({
  type: z.enum([NotificationTypeE.Enum.REQUEST_ACCEPTED]),
  actorProfileId: UUIDSchema,
  actorProfileName: ActorProfileNameSchema,
});
export type NotificationRequestAcceptedI = z.infer<typeof NotificationRequestAcceptedSchema>;

export const NotificationFetchManyResultItemSchema = z.object({
  actorProfileId: UUIDSchema,
  postId: UUIDSchema.optional(),
});
export type NotificationFetchManyResultItemI = z.infer<typeof NotificationFetchManyResultItemSchema>;

export const NotificationFetchManySchema = z.object({
  items: z.array(NotificationFetchManyResultItemSchema),
});
export type NotificationFetchManyI = z.infer<typeof NotificationFetchManySchema>;

export const NotificationCreateOneSchema = z
  .object({
    actorProfileId: UUIDSchema,
    actorProfileName: ActorProfileNameSchema,
    answerId: UUIDSchema.optional(),
    answerText: PostTextSchema.optional(),
    commentId: UUIDSchema.optional(),
    firebaseTopic: NotificationTopicE.optional(),
    parentCommentId: UUIDSchema.optional(),
    postId: UUIDSchema.optional(),
    postText: PostTextSchema.optional(),
    profileId: UUIDSchema.optional(),
    profileIds: z.array(UUIDSchema).min(1).optional(),
    questionsCount: z.number().int().min(1).optional(),
    questionId: UUIDSchema.optional(),
    questionTitle: PostTextSchema.optional(),
    requestId: UUIDSchema.optional(),
    screen: NotificationScreenE.optional(),
    topic: KafkaTopicE,
    type: NotificationTypeE,
    voteType: VoteTypeE.optional(),
  })
  .partial();
export type NotificationCreateOneI = z.infer<typeof NotificationCreateOneSchema>;
