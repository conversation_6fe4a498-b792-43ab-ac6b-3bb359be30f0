import AppError from '@classes/AppError';
import { ScoreTypeE as ScoreType } from '@prisma/postgres';

export const ScoreTypeToFieldMap = {
  [ScoreType.CONTRIBUTION]: 'contributionScore',
  [ScoreType.INVITE_CONTRIBUTION]: 'contributionScore',

  [ScoreType.QNA_ANSWER]: 'qnaAnswerScore',
  [ScoreType.QNA_ANSWER_VERIFIED]: 'qnaAnswerScore',
  [ScoreType.QNA_ANSWER_LESS_THAN_TWO_HOURS]: 'qnaAnswerScore',
  [ScoreType.QNA_ANSWER_TWO_TO_FOUR_HOURS]: 'qnaAnswerScore',
  [ScoreType.QNA_ANSWER_FOUR_TO_SIX_HOURS]: 'qnaAnswerScore',
  [ScoreType.QNA_ANSWER_SIX_TO_EIGHT_HOURS]: 'qnaAnswerScore',
  [ScoreType.QNA_ANSWER_EIGHT_TO_TWENTYFOUR_HOURS]: 'qnaAnswerScore',

  [ScoreType.TROUBLESHOOT_ANSWER]: 'troubleshootAnswerScore',
  [ScoreType.TROUBLESHOOT_ANSWER_VERIFIED]: 'troubleshootAnswerScore',
  [ScoreType.TROUBLESHOOT_ANSWER_LESS_THAN_TWO_HOURS]: 'troubleshootAnswerScore',
  [ScoreType.TROUBLESHOOT_ANSWER_TWO_TO_FOUR_HOURS]: 'troubleshootAnswerScore',
  [ScoreType.TROUBLESHOOT_ANSWER_FOUR_TO_SIX_HOURS]: 'troubleshootAnswerScore',
  [ScoreType.TROUBLESHOOT_ANSWER_SIX_TO_EIGHT_HOURS]: 'troubleshootAnswerScore',
  [ScoreType.TROUBLESHOOT_ANSWER_EIGHT_TO_TWENTYFOUR_HOURS]: 'troubleshootAnswerScore',
} as const;

export type ScoreFieldKey = 'contributionScore' | 'qnaAnswerScore' | 'troubleshootAnswerScore';

export const getScoreField = (type: ScoreType): ScoreFieldKey => {
  const field = ScoreTypeToFieldMap[type];
  if (!field) {
    console.error(`ScoreType '${type}' is not mapped to a reward profile field`);
    throw new AppError('RWD005');
  }
  return field;
};
