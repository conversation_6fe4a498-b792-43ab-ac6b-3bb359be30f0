import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import PolicyModule from '@modules/policy/policy';
import { PolicyFetchSchema } from '@schemas/policy/policy';
import type { FastifyInstance, FastifyReply } from 'fastify';

const policyFetchRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/policy', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: queryData, error: queryError } = PolicyFetchSchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('GEN002', queryError);
    }
    const result = await PolicyModule.fetch(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default policyFetchRoutes;
