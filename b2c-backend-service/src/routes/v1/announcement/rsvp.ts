import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import { AnnouncementModule } from '@modules/announcement';
import { RSVPFetchPeopleSchema, RSVPUpsertSchema } from '@schemas/announcement/rsvp';
import { FastifyInstance, FastifyReply } from 'fastify';

const rsvpRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/announcement/nearby', {}, async (_request: FastifyRequestI, _reply: FastifyReply) => {
    // const { data: bodyData, error: bodyError } = NearByFetchPeopleBodySchema.safeParse(request.body);
    // if (bodyError) {
    //   throw new AppError('PFL008', { error: bodyError.errors });
    // }
    // const result = await AnnouncementModule.NearByModule.fetchPeople(request, bodyData);
    // reply.status(HttpStatus.OK).send(result);
  });
  fastify.post('/backend/api/v1/announcement/rsvp', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data: bodyData, error: bodyError } = RSVPUpsertSchema.safeParse(request.body);
    if (bodyError) {
      throw new AppError('RSVP003', { error: bodyError.errors });
    }

    await AnnouncementModule.RSVPModule.upsert(request, bodyData);

    reply.status(HttpStatus.OK);
  });
  fastify.get(
    '/backend/api/v1/announcement/rsvp/profiles',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { data: queryData, error: queryError } = RSVPFetchPeopleSchema.safeParse(request.query);

      if (queryError) {
        throw new AppError('RSVP003', { error: queryError.errors });
      }

      const result = await AnnouncementModule.RSVPModule.fetchProfiles(request, queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
};

export default rsvpRoutes;
