import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { EntityModuleFetchsertParamsI } from '@interfaces/company/entity';
import Company from '@modules/company';
import { EntityOptionsFetchSchema, EntityNameSchema} from '@schemas/company/entity';
import { pick } from '@utils/data/object';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';


const entityRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/company/entity/institute/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const query = EntityOptionsFetchSchema.parse(request.query);
      const result = await Company.EntityModule.fetchForClient(
        query.search,
        'EDUCATION',
        pick(query, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/company/entity/organization/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = EntityOptionsFetchSchema.safeParse(request.query);
      if (queryError) {
        throw new AppError('GEN005', queryError);
      }
      const result = await Company.EntityModule.fetchForClient(
        queryData.search,
        'COMPANY',
        pick(queryData, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/company/entity/all/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const query = EntityOptionsFetchSchema.parse(request.query);
      const result = await Company.EntityModule.fetchForClient(
        query.search,
        undefined,
        pick(query, ['page', 'pageSize']),
      );
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.post(
    '/backend/api/v1/company/entity/all/options',
    {},
    async (request: FastifyRequest, reply: FastifyReply) => {
      const body = EntityNameSchema.parse(request.body);
      const result = await Company.EntityModule.fetchsert(body as EntityModuleFetchsertParamsI);
      reply.status(HttpStatus.OK).send(result);
    },
  );


};

export default entityRoutes;
