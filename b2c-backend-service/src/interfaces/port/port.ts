import { DBDataTypeI } from '@consts/common/data';
import { NullableI, NumberNullI, StringNullI, StringUndefinedI } from '@interfaces/common/data';
import { CityNestedClientI } from '@interfaces/master/city';
import type { City, Country, Port, PortRawData, Timezone } from '@prisma/postgres';
export type PortSearchClientI = Pick<PortRawData, 'name' | 'unLocode'> & {
  city: CityNestedClientI;
  dataType: DBDataTypeI;
};
export type PortRawQueryFetchForClientResultI = {
  unLocode: StringNullI;
  name: StringNullI;
  countryIso2: StringNullI;
  countryName: StringNullI;
  cityId: StringNullI;
  cityName: StringNullI;
  cityRawDataId: StringNullI;
  cityRawName: StringNullI;
  dataType: NullableI<DBDataTypeI>;
};
export type PortRawQueryFetchsertResultI = {
  unLocode: string;
  name: string;
  countryIso2: string;
  countryName: string;
  cityId: StringNullI;
  cityName: StringNullI;
  cityRawDataId: StringNullI;
  cityRawName: StringNullI;
  dataType: DBDataTypeI;
};
export type PortClientI = Port & {
  dataType: DBDataTypeI;
};
export type FetchByUnLocodeDetailedRawDataI = {
  unLocode: string;
  name: string;
  imageUrl: StringNullI;
  latitude: NumberNullI;
  longitude: NumberNullI;
  noOfTerminals: NumberNullI;
  noOfBerths: NumberNullI;
  maxDraught: NumberNullI;
  maxDeadweight: NumberNullI;
  maxLength: NumberNullI;
  maxAirDraught: NumberNullI;
  countryIso2: string;
  countryName: string;
  cityId: StringNullI;
  cityName: StringNullI;
  cityRawDataId?: StringUndefinedI;
  cityRawDataName?: StringUndefinedI;
  isVisited: boolean;
  dataType: DBDataTypeI;
  timezone: StringNullI;
  utcOffset: NumberNullI;
  dstOffset: NumberNullI;
};
export type FetchByUnLocodeDetailedResultI = Omit<
  FetchByUnLocodeDetailedRawDataI,
  | 'countryIso2'
  | 'countryName'
  | 'cityId'
  | 'cityName'
  | 'cityRawDataId'
  | 'cityRawDataName'
  | 'timezone'
  | 'utcOffset'
  | 'dstOffset'
> & {
  country: Pick<Country, 'name'>;
  city?: NullableI<Pick<City, 'name'>>;
  timezone?: Pick<Timezone, 'timezone' | 'utcOffset' | 'dstOffset'>;
  dataType: DBDataTypeI;
};

export type PortSearchClientWithCoordinatesI = Pick<PortRawData, 'name' | 'unLocode' | 'latitude' | 'longitude'> & {
  city: CityNestedClientI;
  dataType: DBDataTypeI;
};

export type PortRawQueryFetchForClientWithCoordinatesResultI = {
  unLocode: StringNullI;
  name: StringNullI;
  countryIso2: StringNullI;
  countryName: StringNullI;
  cityId: StringNullI;
  cityName: StringNullI;
  cityRawDataId: StringNullI;
  cityRawName: StringNullI;
  dataType: NullableI<DBDataTypeI>;
  longitude: StringNullI;
  latitude: StringNullI;
};
