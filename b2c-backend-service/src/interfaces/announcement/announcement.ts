import { CityNestedClientI } from '@interfaces/master/city';
import { ProfileDataI, ProfileExternalI } from '@interfaces/user/profile';
import { Announcement } from '@prisma/postgres';

export type CoreAnnouncementFetchItemSQLI = Announcement & {
  distanceInMeters?: number;
  isRSVPed: boolean;
  profile: ProfileDataI;
  totalAttendees: number;
  topAttendeesRaw: { id: string; name: string }[] | null;
  addressRawData?: {
    id: string;
    text: string;
  };
  cityName?: string;
  cityRawDataName?: string;
};

export type CoreAnnouncementFetchItemI = {
  id: string;
  cursorId: number;
  title: string;
  description: string;
  latitude?: number;
  longitude?: number;
  address?: string;
  city?: CityNestedClientI;
  countryIso2: string;
  startDate: Date;
  endDate: Date;
  startTime: Date;
  endTime: Date;
  distanceInMeters?: number;
  isRSVPed: boolean;
  profile: ProfileExternalI;
  totalAttendees: number;
  attendeesSummary: {
    topAttendees: string[];
    othersCount: number;
  };
};
